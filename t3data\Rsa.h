#include <openssl/rsa.h>
#include <openssl/pem.h>
#include <openssl/bio.h>
#include <openssl/buffer.h>

char *hex2str(const unsigned char *hex, int length) {
    //将十六进制转换为字符串
    char *str = (char *)malloc(length * 2 + 1);
    for (int i = 0; i < length; i++) {
        sprintf(str + i * 2, "%02x", hex[i]);
    }
    str[length * 2] = 0;
    return str;
}

unsigned char *str2hex(const char *str, int length) {
    //将字符串转换为十六进制
    unsigned char *hex = (unsigned char *)malloc(length / 2);
    for (int i = 0; i < length / 2; i++) {
        sscanf(str + i * 2, "%02x", hex + i);
    }
    return hex;
}

char *base64_encode(const unsigned char *input, int length) {
    //base64编码
    BIO *bio, *b64;
    BUF_MEM *bufferPtr;
    char *base64_text;

    b64 = BIO_new(BIO_f_base64());
    BIO_set_flags(b64, BIO_FLAGS_BASE64_NO_NL);
    bio = BIO_new(BIO_s_mem());
    bio = BIO_push(b64, bio);
    BIO_write(bio, input, length);
    BIO_flush(bio);
    BIO_get_mem_ptr(bio, &bufferPtr);
    BIO_set_close(bio, BIO_NOCLOSE);

    base64_text = (char *)malloc(bufferPtr->length + 1);
    memcpy(base64_text, bufferPtr->data, bufferPtr->length);
    base64_text[bufferPtr->length] = 0;

    BIO_free_all(bio);

    return base64_text;
}

unsigned char *base64_decode(const char *input, int length) {
    //base64编码
    BIO *bio, *b64;
    unsigned char *buffer = (unsigned char *)malloc(length);
    
    b64 = BIO_new(BIO_f_base64());
    BIO_set_flags(b64, BIO_FLAGS_BASE64_NO_NL);
    bio = BIO_new_mem_buf(input, length);
    bio = BIO_push(b64, bio);
    BIO_read(bio, buffer, length);
    
    BIO_free_all(bio);
    return buffer;
}

char* public_encrypt(const char *message, const char *public_key_pem) {

    // 使用公钥加密
    RSA *public_rsa = NULL;

    BIO *bio = BIO_new_mem_buf(public_key_pem, -1);

    if (bio == NULL) {
        fprintf(stderr, "无法创建BIO对象\n");
        return NULL;
    }

    public_rsa = PEM_read_bio_RSA_PUBKEY(bio, &public_rsa, NULL, NULL);

    if (public_rsa == NULL) {
        fprintf(stderr, "无法加载RSA公钥\n");
        BIO_free(bio);
        return NULL;
    }
    int message_len = strlen(message);
    int block_size = RSA_size(public_rsa) - 11;
    int num_blocks = message_len / block_size + 1;
    unsigned char *cipher_text = (unsigned char *)malloc(RSA_size(public_rsa) * num_blocks);
    int encrypted_len = 0;

    for (int i = 0; i < num_blocks; i++) {
        int block_len = (i == num_blocks - 1) ? message_len % block_size : block_size;
        int result = RSA_public_encrypt(block_len, (unsigned char *)(message + i * block_size), cipher_text + i * RSA_size(public_rsa), public_rsa, RSA_PKCS1_PADDING);
        if (result == -1) {
            fprintf(stderr, "加密失败\n");
            return NULL;
        }
        encrypted_len += result;
    }

	char* base64_encoded = base64_encode((const unsigned char *)cipher_text, encrypted_len); // 使用base64编码

    free(cipher_text);
    return base64_encoded;
    // char* hex_encoded = hex2str(cipher_text, encrypted_len); // 使用十六进制编码

    // free(cipher_text);
    // return hex_encoded;
}

char* public_decrypt(const char *base64_cipher, const char *public_key_pem) {
    // 使用公钥解密
    RSA *public_rsa = NULL;

    BIO *bio = BIO_new_mem_buf(public_key_pem, -1);

    if (bio == NULL) {
        fprintf(stderr, "无法创建BIO对象\n");
        return NULL;
    }

    public_rsa = PEM_read_bio_RSA_PUBKEY(bio, &public_rsa, NULL, NULL);

    if (public_rsa == NULL) {
        fprintf(stderr, "无法加载RSA公钥\n");
        BIO_free(bio);
        return NULL;
    }
    unsigned char *decoded_cipher = base64_decode(base64_cipher, strlen(base64_cipher));
    int block_size_public = RSA_size(public_rsa);
    int num_blocks = (int)strlen(base64_cipher) / block_size_public;
    int decrypted_len = 0;
    char* decrypted_result = NULL;
    unsigned char *result = (unsigned char *)malloc(RSA_size(public_rsa) * num_blocks);

    for (int i = 0; i < num_blocks; i++) {
        int decoded_offset = i * block_size_public;
        int result_offset = i * block_size_public;
        int res = RSA_public_decrypt(block_size_public, decoded_cipher + decoded_offset, result + result_offset, public_rsa, RSA_PKCS1_PADDING);
        if (res != -1) {
            char* temp = (char*)malloc(decrypted_len + res + 1);
            if (decrypted_result != NULL) {
                strcpy(temp, decrypted_result);
                free(decrypted_result);
            } else {
                temp[0] = '\0';
            }
            decrypted_result = temp;
            memcpy(decrypted_result + decrypted_len, result + result_offset, res);
            decrypted_len += res;
            decrypted_result[decrypted_len] = '\0';
        }
    }

    free(decoded_cipher);
    free(result);
    RSA_free(public_rsa);

    return decrypted_result;
}