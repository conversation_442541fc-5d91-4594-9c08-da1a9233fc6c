﻿#include "t3data/t3data.h"

/*
 * T3网络验证 C++ Rsa算法 SDK 示例代码
 * 此代码演示了如何使用T3网络验证 C++ SDK进行单码登录、获取变量、获取版本号、获取公告和心跳验证等操作。
 * 请确保在使用前已正确配置/t3data/t3config.h头文件中的相关路径等。
 * 注意：此代码仅供学习和参考，实际使用时请根据需要进行修改和完善。
 * 网站：https://t3yanzheng.com
 * 使用此例子请确保后台将程序设置为以下配置，没有提到的配置请不要设置
 *
 *  一.本SDK需要用到以下接口，如果程序没有以下接口请先创建接口，并将接口开启安全传输和响应时间戳，成功状态码为200：
 * 【单码用户登录(singleLogin)】
 * 【判断单码卡密登录状态码是否有效(isSingleLoginStatus)】
 * 【获取程序公告(getProgramNotice)】
 * 【获取程序最新版本号(getProgramVersionNumber)】
 * 【获取变量内容(getValueContent)】
 *
 *  二.本SDK需要程序配置中做出以下配置：
 * 【通信安全】：
 *  1.开启安全传输
 *  2.将程序密钥填写在/t3data/t3config.h中AppKey字段
 *  3.将加密算法选择为Rsa
 *  4.将Rsa公钥填写在/t3data/t3config.h中RsaPublicKey字段
 * 【请求配置】：
 *  1.开启请求参数解密
 *  2.将请求参数解码设置为Base64
 *  3.开启请求参数签名验证
 *  4.开启请求参数时间戳验证
 *  【响应配置】：
 *  1.开启响应数据加密
 *  2.将签名字段方案设置为方案二
 *  3.将响应数据格式设置为json
 *  4.将响应状态码类型设置为Integer
 *  5.开启响应时间戳
 */


/*
 * 编译执行命令，可正常编译，请忽略编译过程中产生的警告
 * g++ -std=c++17 main.cpp -o main -lssl -lcrypto
 * clang++ -std=c++11 main.cpp -o main -lssl -lcrypto
 *
 * 本SDK需要使用openssl库，请确保您的编译环境中已安装这个库。如果无法使用openssl库，请在后台中下载非rsa版本的C++ SDK。
 *
 * 如使用cmake编译，请在中添加以下内容：
 *
 * cmake_minimum_required(VERSION 3.28)
project(untitled2)

set(CMAKE_CXX_STANDARD 17)

# 查找OpenSSL包
find_package(OpenSSL REQUIRED)

# 添加可执行文件
add_executable(untitled2 main.cpp)

# 链接OpenSSL库
target_link_libraries(untitled2 PRIVATE OpenSSL::SSL OpenSSL::Crypto)
 *
 *
 * 建议您先不要修改任何配置，直接编译运行此代码，查看是否能正常执行，本代码已接入测试程序确保可正常使用，登录卡密O8374DDDF13C62975BAF87A4D0C2DE38
 */

int intervalInSeconds = 10000;  // 心跳验证间隔
std::string card_kami;//卡密
std::string heartbeat_code;//心跳状态码
std::string machine_code = "test";//设备码
std::string value_id = "3247";//后台创建一个变量的ID
std::string value_name = "nb666";//后台创建一个变量的名称
std::string loacl_version = "1000";//程序当前版本版本号


void getValue()
{
	T3DATA getValueApi;//创建一个客户端接口实例对象
	getValueApi.setRequestApi(T3Config::Path_GetValueContent); //设置接口请求的地址的调用码
	getValueApi.addRequestParam("kami", card_kami); //传入卡密参数
	getValueApi.addRequestParam("valueid", value_id); //传入变量ID
	getValueApi.addRequestParam("valuename", value_name); //传入变量名称
	bool responseResult = getValueApi.sendRequest();
	if (!responseResult) {//请求处理失败
		std::cout << "请求失败" << std::endl; return;
	}
	try
	{
		Json::Value responseJson = getValueApi.getResponseJsonObject();//获取响应json对象
		int responseCode = responseJson["code"].asInt();
		if (responseCode != 200) {//接口业务码响应非成功-将服务器返回信息提示
			std::string responseMessage = responseJson["msg"].asString();
			std::cout << "接口业务码响应失败：" << responseMessage << std::endl; return;
		}
		std::cout << "服务器响应获取变量成功，开始验证" << std::endl;
		if (!getValueApi.requestSafeCodeVerify()) {//防劫持验证检测未通过
			std::cout << "检测到数据被劫持篡改了" << std::endl; return;
		}
		std::cout << "防劫持验证通过" << std::endl;
		if (!getValueApi.requestDataTimeDifferenceVerify()) {//响应数据时差验证不通过
			std::cout << "响应数据异常，与服务器时差相差过多" << std::endl; return;
		}
		std::cout << "时间戳验证通过" << std::endl;
		std::string responseMessage = responseJson["msg"].asString();

		std::cout << "获取变量成功\n变量内容：" << responseMessage << std::endl;
	}
	catch (const std::exception& ex)
	{
		std::cout << "处理失败，出现异常" << std::endl; return;
	}
}

void getVersion()
{
	T3DATA getVersionApi;//创建一个客户端接口实例对象
	getVersionApi.setRequestApi(T3Config::Path_GetProgramVersionNumber); //设置接口请求的地址的调用码
	bool responseResult = getVersionApi.sendRequest();
	if (!responseResult) {//请求处理失败
		std::cout << "请求失败" << std::endl; return;
	}
	try
	{
		Json::Value responseJson = getVersionApi.getResponseJsonObject();//获取响应json对象
		int responseCode = responseJson["code"].asInt();
		if (responseCode != 200) {//接口业务码响应非成功-将服务器返回信息提示
			std::string responseMessage = responseJson["msg"].asString();
			std::cout << "接口业务码响应失败：" << responseMessage << std::endl; return;
		}
		std::cout << "服务器响应获取版本号成功，开始验证" << std::endl;
		if (!getVersionApi.requestSafeCodeVerify()) {//防劫持验证检测未通过
			std::cout << "检测到数据被劫持篡改了" << std::endl; return;
		}
		std::cout << "防劫持验证通过" << std::endl;
		if (!getVersionApi.requestDataTimeDifferenceVerify()) {//响应数据时差验证不通过
			std::cout << "响应数据异常，与服务器时差相差过多" << std::endl; return;
		}
		std::cout << "时间戳验证通过" << std::endl;
		std::string responseMessage = responseJson["msg"].asString();

		 std::cout << "获取版本号成功\n最新版本号：" << responseMessage << std::endl;
		 std::cout << "您当前版本\n版本号：" << loacl_version << std::endl;

		if(loacl_version < responseMessage){
			std::cout << "版本过低" << std::endl; return;
		}else{
			std::cout << "版本正常" << std::endl; return;
		}

	}
	catch (const std::exception& ex)
	{
		std::cout << "处理失败，出现异常" << std::endl; return;
	}
}


void getNotice()
{
	T3DATA getNoticeApi;//创建一个客户端接口实例对象
	getNoticeApi.setRequestApi(T3Config::Path_GetProgramNotice); //设置接口请求的地址的调用码
	bool responseResult = getNoticeApi.sendRequest();
	if (!responseResult) {//请求处理失败
		std::cout << "请求失败" << std::endl; return;
	}
	try
	{
		Json::Value responseJson = getNoticeApi.getResponseJsonObject();//获取响应json对象
		int responseCode = responseJson["code"].asInt();
		if (responseCode != 200) {//接口业务码响应非成功-将服务器返回信息提示
			std::string responseMessage = responseJson["msg"].asString();

			std::cout << "接口业务码响应失败：" << responseMessage << std::endl; return;
		}
		std::cout << "服务器响应公告成功，开始验证" << std::endl;
		if (!getNoticeApi.requestSafeCodeVerify()) {//防劫持验证检测未通过
			std::cout << "检测到数据被劫持篡改了" << std::endl; return;
		}
		std::cout << "防劫持验证通过" << std::endl;
		if (!getNoticeApi.requestDataTimeDifferenceVerify()) {//响应数据时差验证不通过
			std::cout << "响应数据异常，与服务器时差相差过多" << std::endl; return;
		}
		std::cout << "时间戳验证通过" << std::endl;
		std::string responseMessage = responseJson["msg"].asString();

		std::cout << "获取公告成功\n公告内容：" << responseMessage << std::endl;
	}
	catch (const std::exception& ex)
	{
		std::cout << "处理失败，出现异常" << std::endl; return;
	}
}

void heartbeat()
{
	T3DATA heartbeatApi;//创建一个客户端接口实例对象
	heartbeatApi.setRequestApi(T3Config::Path_IsSingleLoginStatus); //设置接口请求的地址的调用码
	heartbeatApi.addRequestParam("kami", card_kami); //传入卡密参数
	heartbeatApi.addRequestParam("statecode", heartbeat_code); //传入心跳状态码
	bool responseResult = heartbeatApi.sendRequest();
	if (!responseResult) {//请求处理失败
		std::cout << "请求失败" << std::endl; return;
	}
	try
	{
		Json::Value responseJson = heartbeatApi.getResponseJsonObject();//获取响应json对象
		int responseCode = responseJson["code"].asInt();
		if (responseCode != 200) {//接口业务码响应非成功-将服务器返回信息提示
			std::string responseMessage = responseJson["msg"].asString();

			std::cout << "接口业务码响应失败：" << responseMessage << std::endl; return;
		}
		std::cout << "服务器响应心跳成功，开始验证" << std::endl;
		if (!heartbeatApi.requestSafeCodeVerify()) {//防劫持验证检测未通过
			std::cout << "检测到数据被劫持篡改了" << std::endl; return;
		}
		std::cout << "防劫持验证通过" << std::endl;
		if (!heartbeatApi.requestDataTimeDifferenceVerify()) {//响应数据时差验证不通过
			std::cout << "响应数据异常，与服务器时差相差过多" << std::endl; return;
		}
		std::cout << "时间戳验证通过" << std::endl;
		std::string responseMessage = responseJson["msg"].asString();
        if(responseMessage == "心跳验证成功" || responseMessage.rfind("心跳验证成功:", 0) == 0) {
            std::cout << "心跳验证通过" << std::endl;
        } else {
            std::cout << "心跳验证失败，应当结束进程" << std::endl;
            std::cout << responseMessage << std::endl;
        }
	}
	catch (const std::exception& ex)
	{
		std::cout << "处理失败，出现异常" << std::endl; return;
	}
}

//单码登录方法
void login()
{
    std::cout << "请输入卡密: ";
    std::getline(std::cin, card_kami); // 获取一整行输入并存储在userInput中
	T3DATA cardLoginApi;//创建一个客户端接口实例对象
	cardLoginApi.setRequestApi(T3Config::Path_SingleLogin); //设置接口请求的地址的调用码
	cardLoginApi.addRequestParam("kami", card_kami); //传入卡密参数
	cardLoginApi.addRequestParam("imei", machine_code); //传入机器码参数
	bool responseResult = cardLoginApi.sendRequest();
	if (!responseResult) {//请求处理失败
		std::cout << "请求失败" << std::endl; return;
	}
	try
	{
		Json::Value responseJson = cardLoginApi.getResponseJsonObject();//获取响应json对象
		int responseCode = responseJson["code"].asInt();
		if (responseCode != 200) {//接口业务码响应非成功-将服务器返回信息提示
			std::string responseMessage = responseJson["msg"].asString();

			std::cout << "接口业务码响应失败：" << responseMessage << std::endl; return;
		}
		std::cout << "服务器响应登录成功，开始验证" << std::endl;
		if (!cardLoginApi.requestDataSignatureVerify()) {//响应数据验签检测未通过
			std::cout << "响应数据验签失败" << std::endl; return;
		}
		std::cout << "数据签名验证通过" << std::endl;
		if (!cardLoginApi.requestSafeCodeVerify()) {//防劫持验证检测未通过
			std::cout << "检测到数据被劫持篡改了" << std::endl; return;
		}
		std::cout << "防劫持验证通过" << std::endl;
		if (!cardLoginApi.requestDataTimeDifferenceVerify()) {//响应数据时差验证不通过
			std::cout << "响应数据异常，与服务器时差相差过多" << std::endl; return;
		}
		std::cout << "时间戳验证通过" << std::endl;
		// std::string expireTimeStr = cJSON_GetObjectItem(responseJson, "end_time")->valuestring; //卡密到期时间字符串
		std::string expireTimeStr = responseJson["end_time"].asString();
		heartbeat_code = responseJson["statecode"].asString();


		std::cout << "单码登录验证成功\n到期时间：" << expireTimeStr << std::endl;
		getValue();
        while (true)
        {
            // 调用心跳函数
            heartbeat();
            std::this_thread::sleep_for(std::chrono::seconds(intervalInSeconds));
        }
	}
	catch (const std::exception& ex)
	{
		std::cout << "处理失败，出现异常" << std::endl; return;
	}
}





//启动方法
int main()
{
	std::cout << "调用版本判断" << std::endl;
	getVersion();
	std::cout << "调用获取公告" << std::endl;
	getNotice();
	std::cout << "调用单码登录" << std::endl;
	login();
	getchar();
	return 0;
}