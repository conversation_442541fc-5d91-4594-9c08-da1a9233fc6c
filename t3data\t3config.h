// T3Config.h
#pragma once

#include <string>

namespace T3Config {
    // t3网络验证配置
    static const std::string Host = "http://w.t3yanzheng.com"; // t3网络验证线路地址
    static const std::string AppKey = "cb4b7354fdad56ba9fc298570f43c5df"; // t3网络验证程序应用密钥
    static const std::string RsaPublicKey = "-----BEGIN PUBLIC KEY-----\n"
                                            "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC1mzZXux6eSuf5Z7g1S6j1l7C8\n"
                                            "EA49kbLIkcswZnI29JnpghbcdEDt73hjNnsl635qRNwSK8q/tYGRS2+U03qUbWGZ\n"
                                            "7xdOlbvoj2LHyzoB4wColmUj9ZnGlwad+z4S6bSP7ttnQ1CAz6zrE12s/jkeKzR7\n"
                                            "PvFtuknMNwCeVp9qwwIDAQAB\n"
                                            "-----END PUBLIC KEY-----";
    // API路径
    static const std::string Path_SingleLogin = "80D88186C97B27A0"; // t3网络验证程序单码卡密登录API调用路径
    static const std::string Path_IsSingleLoginStatus = "3550AD34AF37B2B7"; // t3网络验证程序单码卡密登录状态查询API调用路径
    static const std::string Path_GetProgramNotice = "36F2EDE1CB3593BB"; // t3网络验证程序获取公告API调用路径
    static const std::string Path_GetProgramVersionNumber = "44253276141A8A22"; // t3网络验证程序获取版本号API调用路径
    static const std::string Path_GetValueContent = "4ACE29BA42B233A1"; // t3网络验证程序获取变量内容API调用路径
}